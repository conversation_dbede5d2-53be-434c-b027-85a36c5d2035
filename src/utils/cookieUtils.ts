/**
 * Utility functions for cookie consent management
 */

import { getCookie, showPreferences } from 'vanilla-cookieconsent';

/**
 * Check if analytics cookies are accepted
 */
export const isAnalyticsAccepted = (): boolean => {
  try {
    const cookie = getCookie();
    return cookie && cookie.categories && cookie.categories.includes('analytics');
  } catch (error) {
    return false;
  }
};

/**
 * Check if necessary cookies are accepted (always true)
 */
export const isNecessaryAccepted = (): boolean => {
  return true; // Necessary cookies are always accepted
};

/**
 * Get all accepted cookie categories
 */
export const getAcceptedCategories = (): string[] => {
  try {
    const cookie = getCookie();
    return cookie && cookie.categories ? cookie.categories : ['necessary'];
  } catch (error) {
    return ['necessary'];
  }
};

/**
 * Check if user has given consent
 */
export const hasGivenConsent = (): boolean => {
  try {
    const cookie = getCookie();
    return cookie && cookie.categories && cookie.categories.length > 0;
  } catch (error) {
    return false;
  }
};

/**
 * Show the cookie preferences modal
 */
export const showCookiePreferences = (): void => {
  try {
    showPreferences();
  } catch (error) {
    // Fallback to window object
    try {
      // @ts-ignore
      if (window.CC && window.CC.showPreferences) {
        // @ts-ignore
        window.CC.showPreferences();
      }
    } catch (fallbackError) {
      console.warn('Could not show cookie preferences');
    }
  }
};
