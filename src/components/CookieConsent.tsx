import { useEffect } from 'react';
import { run, getCookie, showPreferences } from 'vanilla-cookieconsent';
import '../styles/cookieConsent.css';

const CookieConsentComponent = () => {
  useEffect(() => {
    // Initialize cookie consent
    run({
      categories: {
        necessary: {
          enabled: true,
          readOnly: true
        },
        analytics: {
          enabled: false,
          readOnly: false,
          autoClear: {
            cookies: [
              {
                name: /^_ga/
              },
              {
                name: '_gid'
              }
            ]
          }
        }
      },
      language: {
        default: 'en',
        translations: {
          en: {
            consentModal: {
              title: 'We use cookies',
              description: 'This website uses essential cookies to ensure its proper operation and analytics cookies to understand how you interact with it. Analytics cookies will only be set after consent.',
              acceptAllBtn: 'Accept all',
              acceptNecessaryBtn: 'Accept necessary only',
              showPreferencesBtn: 'Manage preferences',
              footer: `
                <a href="/privacy">Privacy Policy</a>
                <a href="/cookie-policy">Cookie Policy</a>
              `
            },
            preferencesModal: {
              title: 'Cookie Preferences',
              acceptAllBtn: 'Accept all',
              acceptNecessaryBtn: 'Accept necessary only',
              savePreferencesBtn: 'Save preferences',
              closeIconLabel: 'Close',
              sections: [
                {
                  title: 'Cookie Usage',
                  description: 'We use cookies to ensure the basic functionalities of the website and to enhance your online experience. You can choose for each category to opt-in/out whenever you want.'
                },
                {
                  title: 'Strictly Necessary Cookies',
                  description: 'These cookies are essential for the proper functioning of the website. Without these cookies, the website would not work properly.',
                  linkedCategory: 'necessary'
                },
                {
                  title: 'Analytics Cookies',
                  description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',
                  linkedCategory: 'analytics'
                }
              ]
            }
          }
        }
      },
      onConsent: ({ cookie }) => {
        // Handle analytics based on consent
        if (cookie.categories.includes('analytics')) {
          // Enable analytics tracking here if needed
          console.log('Analytics cookies accepted');
        }
      }
    });

    // Expose functions globally for footer button
    // @ts-ignore
    window.CC = {
      getCookie,
      showPreferences
    };
  }, []);

  return null;
};

export default CookieConsentComponent;
