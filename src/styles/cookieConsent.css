/* <PERSON><PERSON> Styles */

/* Main container */
#cc-main {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', '<PERSON>bunt<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  z-index: 999999 !important;
}

/* Consent modal */
#cc-main .cm {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  max-width: 420px !important;
  margin: 1rem !important;
}

/* Header */
#cc-main .cm__header {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 1.5rem !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Title */
#cc-main .cm__title {
  color: #1f2937 !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 0.75rem 0 !important;
  line-height: 1.4 !important;
}

/* Description */
#cc-main .cm__desc {
  color: #4b5563 !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Body */
#cc-main .cm__body {
  padding: 0 1.5rem !important;
}

/* Footer */
#cc-main .cm__footer {
  padding: 1.5rem !important;
  border-top: 1px solid #e2e8f0 !important;
  background-color: #ffffff !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
  display: flex !important;
  gap: 0.75rem !important;
  flex-wrap: wrap !important;
  justify-content: flex-end !important;
}

/* Buttons */
#cc-main .cm__btn {
  border-radius: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding: 0.625rem 1.25rem !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 40px !important;
  white-space: nowrap !important;
}

/* Primary button (Accept all) */
#cc-main .cm__btn--primary {
  background-color: #0d9488 !important;
  color: white !important;
  border: 2px solid #0d9488 !important;
}

#cc-main .cm__btn--primary:hover {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
}

/* Secondary button (Accept necessary only) */
#cc-main .cm__btn--secondary {
  background-color: #ffffff !important;
  color: #374151 !important;
  border: 2px solid #d1d5db !important;
}

#cc-main .cm__btn--secondary:hover {
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
}

/* Link button (Manage preferences) */
#cc-main .cm__btn--link {
  background-color: transparent !important;
  color: #0d9488 !important;
  border: none !important;
  text-decoration: underline !important;
  padding: 0.5rem !important;
}

#cc-main .cm__btn--link:hover {
  color: #0f766e !important;
}

/* Preferences modal */
#cc-main .pm {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  max-width: 600px !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
}

/* Preferences modal header */
#cc-main .pm__header {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 1.5rem !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Preferences modal title */
#cc-main .pm__title {
  color: #1f2937 !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

/* Preferences modal body */
#cc-main .pm__body {
  padding: 1.5rem !important;
}

/* Preferences modal footer */
#cc-main .pm__footer {
  padding: 1.5rem !important;
  border-top: 1px solid #e2e8f0 !important;
  background-color: #f8fafc !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
  display: flex !important;
  gap: 0.75rem !important;
  justify-content: flex-end !important;
}
