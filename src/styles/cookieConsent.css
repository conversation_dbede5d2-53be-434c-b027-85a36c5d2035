/* <PERSON><PERSON> Styles */

/* Hide unwanted elements */
.pm-wrapper.cc--ani,
.pm-wrapper,
[class*="pm-wrapper"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Main container - positioned at bottom of viewport */
#cc-main {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  z-index: 999999 !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: flex-end !important;
  padding: 1rem !important;
  pointer-events: none !important; /* Allow clicks through the container */
}

/* Consent modal */
#cc-main .cm {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1) !important;
  max-width: 500px !important;
  width: 100% !important;
  margin: 0 !important;
  pointer-events: auto !important; /* Re-enable clicks on the modal */
}

/* Header */
#cc-main .cm__header {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 2rem 1.5rem 1.5rem 1.5rem !important; /* Added top padding */
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Title */
#cc-main .cm__title {
  color: #1f2937 !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 0 0.75rem 0 !important;
  line-height: 1.4 !important;
}

/* Description */
#cc-main .cm__desc {
  color: #4b5563 !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Body */
#cc-main .cm__body {
  padding: 0 1.5rem !important;
}

/* Footer */
#cc-main .cm__footer {
  padding: 1.5rem !important;
  border-top: 1px solid #e2e8f0 !important;
  background-color: #ffffff !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
  display: flex !important;
  gap: 0.75rem !important;
  flex-wrap: wrap !important;
  justify-content: flex-end !important;
}

/* Buttons */
#cc-main .cm__btn {
  border-radius: 0.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding: 0.625rem 1.25rem !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 40px !important;
  white-space: nowrap !important;
}

/* Primary button (Accept all) */
#cc-main .cm__btn--primary {
  background-color: #0d9488 !important;
  color: white !important;
  border: 2px solid #0d9488 !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(13, 148, 136, 0.2) !important;
  transform: translateY(0) !important;
  transition: all 0.2s ease-in-out !important;
}

#cc-main .cm__btn--primary:hover {
  background-color: #0f766e !important;
  border-color: #0f766e !important;
  box-shadow: 0 4px 8px rgba(13, 148, 136, 0.3) !important;
  transform: translateY(-1px) !important;
}

#cc-main .cm__btn--primary:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(13, 148, 136, 0.2) !important;
}

/* Secondary button (Accept necessary only) */
#cc-main .cm__btn--secondary {
  background-color: #ffffff !important;
  color: #374151 !important;
  border: 2px solid #d1d5db !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(0) !important;
  transition: all 0.2s ease-in-out !important;
}

#cc-main .cm__btn--secondary:hover {
  background-color: #f9fafb !important;
  border-color: #9ca3af !important;
  color: #1f2937 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-1px) !important;
}

#cc-main .cm__btn--secondary:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Link button (Manage preferences) */
#cc-main .cm__btn--link {
  background-color: transparent !important;
  color: #0d9488 !important;
  border: none !important;
  text-decoration: underline !important;
  padding: 0.5rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease-in-out !important;
}

#cc-main .cm__btn--link:hover {
  color: #0f766e !important;
  text-decoration-thickness: 2px !important;
  transform: translateY(-1px) !important;
}

/* Preferences modal */
#cc-main .pm {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  max-width: 600px !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
}

/* Preferences modal header */
#cc-main .pm__header {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 1.5rem !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* Preferences modal title */
#cc-main .pm__title {
  color: #1f2937 !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
}

/* Preferences modal body */
#cc-main .pm__body {
  padding: 1.5rem !important;
}

/* Preferences modal footer */
#cc-main .pm__footer {
  padding: 1.5rem !important;
  border-top: 1px solid #e2e8f0 !important;
  background-color: #f8fafc !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
  display: flex !important;
  gap: 0.75rem !important;
  justify-content: flex-end !important;
}

/* Section styling */
#cc-main .pm__section {
  margin-bottom: 1.5rem !important;
}

#cc-main .pm__section:last-child {
  margin-bottom: 0 !important;
}

/* Section title */
#cc-main .section__title {
  color: #1f2937 !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  margin: 0 0 0.5rem 0 !important;
}

/* Section description */
#cc-main .section__desc {
  color: #4b5563 !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Toggle switch */
#cc-main .toggle {
  position: relative !important;
  display: inline-block !important;
  width: 48px !important;
  height: 24px !important;
}

#cc-main .toggle input {
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

#cc-main .toggle__slider {
  position: absolute !important;
  cursor: pointer !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: #d1d5db !important;
  transition: 0.2s !important;
  border-radius: 24px !important;
}

#cc-main .toggle__slider:before {
  position: absolute !important;
  content: "" !important;
  height: 20px !important;
  width: 20px !important;
  left: 2px !important;
  bottom: 2px !important;
  background-color: white !important;
  transition: 0.2s !important;
  border-radius: 50% !important;
}

#cc-main input:checked + .toggle__slider {
  background-color: #0d9488 !important;
}

#cc-main input:checked + .toggle__slider:before {
  transform: translateX(24px) !important;
}

/* Overlay - darker background to make cookie consent more visible */
#cc-main .cc__overlay {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(3px) !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 999998 !important;
}

/* Alternative: Add a subtle overlay behind the consent modal when no overlay exists */
#cc-main::before {
  content: '' !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(2px) !important;
  z-index: -1 !important;
  pointer-events: none !important;
}

/* Links in footer */
#cc-main .cm__footer a,
#cc-main .pm__footer a {
  color: #0d9488 !important;
  text-decoration: underline !important;
  font-size: 0.875rem !important;
}

#cc-main .cm__footer a:hover,
#cc-main .pm__footer a:hover {
  color: #0f766e !important;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  #cc-main {
    padding: 0.5rem !important;
  }

  #cc-main .cm {
    max-width: calc(100vw - 1rem) !important;
    margin: 0 !important;
  }

  #cc-main .pm {
    max-width: calc(100vw - 1rem) !important;
    margin: 0 !important;
    max-height: calc(100vh - 1rem) !important;
  }

  #cc-main .cm__footer,
  #cc-main .pm__footer {
    flex-direction: column !important;
    align-items: stretch !important;
  }

  #cc-main .cm__btn {
    width: 100% !important;
    justify-content: center !important;
  }
}
